server:
  http:
    addr: 0.0.0.0:8000
    timeout: 50s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 50s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://19f047c0280b4ef29b3b051ef0cd5e57@172.16.16.209:9000/21
# mysql配置
mysql:
  dsn: dsers_product_core_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/dsers_product_core_new?loc=Local&parseTime=true&charset=utf8mb4
mysql_seller:
  dsn: dsers_product_seller_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/dsers_product_seller_new?loc=Local&parseTime=true&charset=utf8mb4
redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73o
  db: 1
# kafka配置
kafka:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  topics:
    - dsers-product-supplier-notify
  # 消费者数量
  consumer_num: 1
stock_kafka:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  topics:
    - dsers_product_sync_stock

notify_consumer_kafka:
  brokers:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  group_id: dsers-product-seller-sync
  topic: dsers-product-supplier-notify
  consumer_num: 2
stock_consumer_kafka:
  brokers:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  group_id: dsers-product-seller-sync
  topic: dsers_product_sync_stock
  consumer_num: 4
common_event_consumer_kafka:
  brokers:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  group_id: dsers-product-seller-sync
  topic: dsers-product-common-job
  consumer_num: 1

# 销售商sdk配置
seller_sdk:
  host: open-app-proxy.dsers-staging.svc.cluster.local:8000
  scheme: http
dsers_product_core:
  address: "dsers-product-core.dsers-staging.svc.cluster.local:9000"
  timeout: 50s
dsers_user_core:
  address: "dsers-user-core.dsers-staging.svc.cluster.local:9000"
  timeout: 25s
dsers_settings_product:
  address: "dsers-settings-product.dsers-staging.svc.cluster.local:9000"
  timeout: 25s
dsers_plan_core:
  address: "dsers-plan-core.dsers-staging.svc.cluster.local:9000"
  timeout: 25s
dsers_product_search:
  address: "dsers-product-search.dsers-staging.svc.cluster.local:9000"
  timeout: 25s
dsers_product_supplier:
  address: "dsers-product-supplier.dsers-staging.svc.cluster.local:9000"
  timeout: 25s
dsers_product_seller:
  address: "dsers-product-seller.dsers-staging.svc.cluster.local:9000"
  timeout: 25s
dsers_settings_order:
  address: "dsers-settings-order.dsers-staging.svc.cluster.local:9000"
  timeout: 25s
dsers_product_mapping:
  address: "dsers-product-mapping.dsers-staging.svc.cluster.local:9000"
  timeout: 25s
open_app_core:
  address: "open-app-core.dsers-staging.svc.cluster.local:9000"
  timeout: 25s


# 每天凌晨1点执行一次："0 0 1 * * ?"
cron_schedule_create_stock: "0 0 9 * * ?"
cron_schedule_sync_stock: "0 20 9 * * ?"
# 定时同步库存总开关  1-打开
cron_sync_stock_switch: 1
sync_stock_pool_size: 40
notify_pool_size: 50

collect_kafka:
  addrs:
    - alikafka-pre-cn-br5428ais003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-3-vpc.alikafka.aliyuncs.com:9092
  topics:
    - data-common-collect-server

webhook_stock_remind: "https://open.feishu.cn/open-apis/bot/v2/hook/3ad271a1-f05f-482a-b75c-9a7f1635d312"
# 定时获取库存同步情况，是否发飞书  1-打开
cron_webhook_stock_remind_switch: 0

woo_app_id: 1656559815622291456
jumpseller_app_id: 1656559719228796928
squarespace_app_id: 1820295175435517952