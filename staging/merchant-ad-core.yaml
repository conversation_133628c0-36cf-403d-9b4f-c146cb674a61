server:
  grpc:
    addr: 0.0.0.0:9000
    timeout: 20s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 10s
  metric:
    addr: 0.0.0.0:8080
    timeout: 10s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces


redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73o
  db: 1 
mysql:
  dsn: merchant_core_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/merchant_core_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true
grpc_clients:
  merchant_pay_core:
    address: merchant-pay-core.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  merchant_user_core:
    address: merchant-user-core.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  infra_id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 20s
dsers_sdk:
  host: open-dsers-supplier-api-gw-staging.topdsers.com
  scheme: http
  client_id: "1657988085740912640"
  client_secret: H-pDrhYXRpMhkw-Iv7PLISx0Ifz4N9efd4AnBNlB0coBzG0XF3SCEyTcIctTCgg=
  token_url: /api/v1/oauth2/token