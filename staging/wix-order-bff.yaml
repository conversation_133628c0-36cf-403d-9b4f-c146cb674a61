env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 5s
  metric:
    addr: 0.0.0.0:8080
    timeout: 5s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces

wix_api_cli:
  address: wix-api-proxy.dsers-staging.svc.cluster.local:9000
  timeout: 100s

wix_order_cli:
  address: wix-order-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s

wix_user_cli:
  address: wix-user-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s

dsers_app:
  client_id: "1656559772274159616"
  client_secret: SdyZ20wJST9a3KS8NHX_x2lhIn9pP3efue7i7LuBXqLdpwWEJlBskCLCuZVMiGA=
  scheme: http
  host: open-dsers-seller-api-gw-staging.topdsers.com
  token_url: /api/v1/oauth2/token

carrier:
  name:
    - Canada Post
    - FedEx
    - USPS
    - UPS
    - DHL

track_url:
  urls:
    - name: 17Track
      url: https://t.17track.net/en#nums=%s
    - name: UPS
      url: https://www.ups.com/track?loc=en_US&tracknum=%s
    - name: AfterShip
      url: https://mindfulstock.aftership.com/%s
    - name: usps
      url: https://tools.usps.com/go/TrackConfirmAction?tLabels=%s