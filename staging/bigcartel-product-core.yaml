# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   staging
#   prod
env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 300s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 300s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 数据库设置
# TODO: 需要向运维部门申请
big_mysql:
  dsn: seller_bigcartel:Mb9qtKrR44ibp64rLGMWaREYdtjBd3ce@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_bigcartel?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
mysql:
  dsn: seller_bigcartel:Mb9qtKrR44ibp64rLGMWaREYdtjBd3ce@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_bigcartel?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
# 应用设置
app_id: 1820389489405263872
# 中台SDK设置
dsers_sdk:
  client_id: "1820389489405263872"
  client_secret: qYtnK6Ff6Kk4Gx22sxtjupqFMN3pJDjCBDc0gSfPhwps6eumNPFlcJX05fH2q1w=
  scheme: http
  host: open-dsers-seller-api-gw-staging.topdsers.com
  token_url: http://open-dsers-seller-api-gw-staging.topdsers.com/api/v1/oauth2/token
# 服务设置
bigcartel_api_proxy:
  address: bigcartel-api-proxy.dsers-staging.svc.cluster.local:9000
  timeout: 300s
bigcartel_auth_core:
  address: bigcartel-auth-core.dsers-staging.svc.cluster.local:9000
  timeout: 300s