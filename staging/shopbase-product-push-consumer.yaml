# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces

# 服务配置
shopbase_product_core:
  address: shopbase-product-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s

shopbase_auth_core:
  address: shopbase-auth-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s

# 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw-staging.topdsers.com
  client_id: "1813749459137069056"
  client_secret: BivHmMvHKTCIdegMEUNR_cF61j1lrs8vog4ewLxU8g6JYY3GAK7c9WFyymjQ1cs=
  token_url: http://open-dsers-seller-api-gw-staging.topdsers.com/api/v1/oauth2/token

# images_dispatcher 配置
images_dispatcher:
  name: shopbase-product-push-images-staging
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
    dial_timeout: 5s

# push_dispatcher 配置
push_dispatcher:
  name: shopbase-product-push-staging
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
    dial_timeout: 5s
