env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 15s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 15s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 15s
  metric:
    addr: 0.0.0.0:8080
    timeout: 15s

redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73o
  db: 2

redis_pre: "infra-proxy-core"
proxy: "http://************:3128"

clients:
  open_app_core:
    address: open-app-core.dsers-staging.svc.cluster.local:9000
    timeout: 20s