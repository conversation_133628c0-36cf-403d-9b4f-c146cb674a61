env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 30s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 30s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 15s
  metric:
    addr: 0.0.0.0:8080
    timeout: 15s
# app 配置
app_conf:
  tmall_app_id: 159831084
  aliexpress_app_id: 159831080
# myusql配置
mysql:
  dsn: supplier_tmall_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/supplier_tmall_new?loc=Local&parseTime=true&charset=utf8mb4
# rpc服务配置
tmall_product_logistics:
  address: "tmall-product-logistics.dsers-staging.svc.cluster.local:9000"
  timeout: 5s
tmall_currency_core:
  address: "tmall-currency-core.dsers-staging.svc.cluster.local:9000"
  timeout: 5s
tmall_product_classify:
  address: "tmall-product-classify.dsers-staging.svc.cluster.local:9000"
  timeout: 5s