---
# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# Kafka设置
kafka:
  addrs:
    - alikafka-pre-cn-br5428ais003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-3-vpc.alikafka.aliyuncs.com:9092
  consumer_num: 2
  order_topic: tiktok-order-hook
  product_topic: tiktok-product-hook
  app_topic: tiktok-app-hook
# 服务设置
tiktok_auth_core:
  address: tiktok-auth-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s
tiktok_product_core:
  address: tiktok-product-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s
tiktok_order_core:
  address: tiktok-order-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s