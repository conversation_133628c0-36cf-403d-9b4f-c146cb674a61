
redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73o
  db: 1
  
id_generator_client:
  address: infra-id-generator.dsers-staging.svc.cluster.local:9000
  timeout: 50s

mysql:
  dsn: dsers_notify_core_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/dsers_notify_core_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true

max_expired: 2592000


product_notify_client:
  address: dsers-product-notify.dsers-staging.svc.cluster.local:9000
  timeout: 50s

dsers_product_search:
  address: dsers-product-search.dsers-staging.svc.cluster.local:9000
  timeout: 50s

kafka:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  topics:
    - dsers-notify-sync-event