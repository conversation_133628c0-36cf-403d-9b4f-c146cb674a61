env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://19f047c0280b4ef29b3b051ef0cd5e57@172.16.16.209:9000/21
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
mysql:
  dsn: jumpseller_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/jumpseller_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true
app_id: 1592406852827308032
open_api:
  client_id: "1656559719228796928"
  client_secret: hHjbUR_N7zQKMjpVBorL__L-611jFXyJ3JnGplEhzxmZYLj40Q1zOP-s9giHJso=
  token_url: http://open-dsers-seller-api-gw-staging.topdsers.com/api/v1/oauth2/token
  scheme: http
  host: open-dsers-seller-api-gw-staging.topdsers.com
jumpseller_api_proxy:
  address: "jumpseller-api-proxy.dsers-staging.svc.cluster.local:9000"
  timeout: 100s
jumpseller_user_core:
  address: "jumpseller-user-core.dsers-staging.svc.cluster.local:9000"
  timeout: 25s
jumpseller_product_replace:
  address: "jumpseller-product-replace.dsers-staging.svc.cluster.local:9000"
  timeout: 100s
redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73o
