env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 1000s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1000s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1000s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1000s


mysql:
  dsn: merchant_core_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/merchant_core_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true
redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73o
  db: 1
grpc_clients:
  dsers_country_city:
    address: dsers-country-city.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  dsers_pay_gateway:
    address: dsers-pay-gateway.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  merchant_product_core:
    address: merchant-product-core.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  merchant_user_core:
    address: merchant-user-core.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  merchant_plan_core:
    address: merchant-plan-core.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  merchant_customer_core:
    address: merchant-customer-core.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  merchant_order_core:
    address: merchant-order-core.dsers-staging.svc.cluster.local:9000
    timeout: 100s  
  infra_id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 100s
  merchant_pay_core:
    address: merchant-pay-core.dsers-staging.svc.cluster.local:9000
    timeout: 100s
  merchant_api1688_proxy:
    address: merchant-api1688-proxy.dsers-staging.svc.cluster.local:9000
    timeout: 100s
dxm_url_prefix: https://test.agencyrouter.com/dsers-dxm-bff/v1/agency/
mb_url_prefix: https://test-mabang.agencyrouter.com
stripe_url: https://devpre.dsers.com/merchant-platform-personal/app/personal/setting/collection
app_id: 1657988085740912640
ebank_url: https://devpre.dsers.com/merchant-platform-personal/app/personal/setting/collection?types=%s
enterprise_ebank_url: https://devpre.dsers.com/merchant-platform/app/enterprise/collect/p-collection?types=%s
enterprise_stripe_url: https://devpre.dsers.com/merchant-platform/app/enterprise/collect/p-collection

kafka:
  addrs:
    - alikafka-pre-cn-br5428ais003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-3-vpc.alikafka.aliyuncs.com:9092
ebank_hook_kafka:
  addrs:
    - alikafka-pre-cn-br5428ais003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-3-vpc.alikafka.aliyuncs.com:9092

user_event_kafka:
  brokers:
    - alikafka-pre-cn-br5428ais003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-3-vpc.alikafka.aliyuncs.com:9092
  group_id: merchant-setting
  topic: merchant-user-event
  consumer_num: 1