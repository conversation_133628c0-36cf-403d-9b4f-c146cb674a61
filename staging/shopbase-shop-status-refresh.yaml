env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces

# 数据库设置
mysql:
  dsn: seller_shopbase:4XsWWLMKLLdTeEQTPNQPaet43rs2xXsT@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_shopbase?charset=utf8mb4&parseTime=True&loc=Local
  debug: false

# 服务配置
shopbase_auth_core:
  address: shopbase-auth-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s

# 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw-staging.topdsers.com
  client_id: "1813749459137069056"
  client_secret: BivHmMvHKTCIdegMEUNR_cF61j1lrs8vog4ewLxU8g6JYY3GAK7c9WFyymjQ1cs=
  token_url: http://open-dsers-seller-api-gw-staging.topdsers.com/api/v1/oauth2/token

