
---
# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: staging
server:
  http:
    addr: 0.0.0.0:8003
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9003
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6063
    timeout: 100s
  metric:
    addr: 0.0.0.0:8083
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 数据库设置
mysql:
  dsn: seller_squarespace:9QRW9dLsKLgtahVrfiVJLwPWE8mzYqDo@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_squarespace?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
# fulfill_event_dispatcher 配置
fulfill_event_dispatcher:
  name: squarespace-fulfill-event-staging
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
    dial_timeout: 5s
# price_dispatcher 配置
price_dispatcher:
  name: squarespace-product-price-staging
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
    dial_timeout: 5s
# stock_dispatcher 配置
stock_dispatcher:
  name: squarespace-product-stock-staging
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
    dial_timeout: 5s
# push_dispatcher 配置
push_dispatcher:
  name: squarespace-product-push-staging
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
    dial_timeout: 5s
