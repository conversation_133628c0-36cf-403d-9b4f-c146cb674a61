env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 200s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 200s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s

services:
  shopify-product-core: shopify-product-core.dsers-staging.svc.cluster.local:9000
  shopify-api-proxy: shopify-api-proxy.dsers-staging.svc.cluster.local:9000

shopify_product_core:
  address: shopify-product-core.dsers-staging.svc.cluster.local:9000
  timeout: 200s

shopify_api_proxy:
  address: shopify-api-proxy.dsers-staging.svc.cluster.local:9000
  timeout: 200s

kafka:
  addrs:
    - alikafka-pre-cn-br5428ais003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-3-vpc.alikafka.aliyuncs.com:9092
  topic_sync_price: shopify-product-sync-price
  topic_event: shopify-events-monitor

redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73o

mysql:
  dsn: seller_shopify_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_shopify_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true

open_api:
  client_id: iyie8Osoodoo5aih2ieJ
  client_secret: mei1iZ2elieB8rubeequephie4de7aihie3ro1ophaifi1mahl
  token_url: http://open-dsers-seller-api-gw-staging.topdsers.com/api/v1/oauth2/token
  scheme: http
  host: open-dsers-seller-api-gw-staging.topdsers.com

kafka_10m:
  addrs:
    - alikafka-pre-cn-br5428ais003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-3-vpc.alikafka.aliyuncs.com:9092
  topic_setting_sync_price: shopify-product-setting-sync-price
