# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 600s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 600s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 600s
  metric:
    addr: 0.0.0.0:8080
    timeout: 600s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 数据库设置
# TODO: 需要向运维部门申请
big_mysql:
  dsn: seller_ecwid:gfLc2peia9sKqNaaaSRaLwH8BfSx@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_ecwid?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
mysql:
  dsn: seller_ecwid:gfLc2peia9sKqNaaaSRaLwH8BfSx@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_ecwid?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
# 应用设置
app_id: "1796361960015974400"
# 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw-staging.topdsers.com
  client_id: "1796361960015974400"
  client_secret: JloJ1SE3Iv01_x0J6EBVzbDlJZdAyd_BR9wzbaV8k83CI4sFCe9hDrmgUZ9l1Wg=
  token_url: http://open-dsers-seller-api-gw-staging.topdsers.com/api/v1/oauth2/token
# 服务设置
ecwid_auth_core:
  address: ecwid-auth-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s
ecwid_api_proxy:
  address: ecwid-api-proxy.dsers-staging.svc.cluster.local:9000
  timeout: 600s
# 打点Kafka设置
kafka_collect:
  addrs:
    - alikafka-pre-cn-br5428ais003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-3-vpc.alikafka.aliyuncs.com:9092
  topic: data-common-collect-server
