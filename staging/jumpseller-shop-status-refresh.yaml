env: staging
server:
  http:
    addr: 0.0.0.0:8001
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9001
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6061
    timeout: 100s
  metric:
    addr: 0.0.0.0:8081
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces

# 数据库设置
mysql:
  dsn: jumpseller_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/jumpseller_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true

# 服务配置
jumpseller_user_core:
  address: jumpseller-user-core.dsers-staging.svc.cluster.local:9000
  timeout: 100s

# 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw-staging.topdsers.com
  client_id: "1656559719228796928"
  client_secret: hHjbUR_N7zQKMjpVBorL__L-611jFXyJ3JnGplEhzxmZYLj40Q1zOP-s9giHJso=
  token_url: http://open-dsers-seller-api-gw-staging.topdsers.com/api/v1/oauth2/token
