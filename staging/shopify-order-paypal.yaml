# env: local
# server:
#   http:
#     addr: 0.0.0.0:8000
#     timeout: 100s
#   grpc:
#     addr: 0.0.0.0:9000
#     timeout: 100s
#   profiling:
#     addr: 0.0.0.0:6060
#     timeout: 30s
#   metric:
#     addr: 0.0.0.0:8080
#     timeout: 30s
# sentry:
#   dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
# trace:
#   endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces

# mysql:
#   dsn: seller_shopify_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_shopify_new?loc=Local&parseTime=true&charset=utf8mb4
#   debug: true
# shopify_order_service:
#   address: "shopify-order-service.dsers-staging.svc.cluster.local:9000"
#   timeout: 100s
# dsers_pay_gateway:
#   address: "dsers-pay-gateway.dsers-staging.svc.cluster.local:9000"
#   timeout: 100s
# shopify_user_core:
#   address: "shopify-user-core.dsers-staging.svc.cluster.local:9000"
#   timeout: 100s

# # kafka配置
# kafka:
#   addrs:
#     - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
#     - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
#     - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
#   # 消费者数量
#   consumer_num: 1
#   topic: "shopify-order-compensate"
#   group: "shopify-order-compensate-consumer"


env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 30s
  metric:
    addr: 0.0.0.0:8080
    timeout: 30s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces

mysql:
  dsn: seller_shopify_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_shopify_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true
shopify_api_proxy:
  address: "shopify-api-proxy.dsers-staging.svc.cluster.local:9000"
  timeout: 100s
dsers_pay_gateway:
  address: "dsers-pay-gateway.dsers-staging.svc.cluster.local:9000"
  timeout: 100s
shopify_user_core:
  address: "shopify-user-core.dsers-staging.svc.cluster.local:9000"
  timeout: 100s

# kafka配置
kafka:
  addrs:
    - alikafka-pre-cn-br5428ais003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-br5428ais003-3-vpc.alikafka.aliyuncs.com:9092
  # 消费者数量
  consumer_num: 3
  topic: "shopify-order-compensate"
  group: "shopify-order-compensate-consumer"
