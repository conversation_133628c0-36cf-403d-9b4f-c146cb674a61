env: staging
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 300s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 300s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s
# 数据库设置
mysql:
  dsn: seller_bigcartel:Mb9qtKrR44ibp64rLGMWaREYdtjBd3ce@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_bigcartel?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
# push_dispatcher 配置
product_push_dispatcher:
  name: bigcartel-product-push-staging
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
    dial_timeout: 5s
image_push_dispatcher:
  name: bigcartel-product-push-images-staging
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-staging.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
    dial_timeout: 5s
bigcartel_auth_core:
  address: bigcartel-auth-core.dsers-staging.svc.cluster.local:9000
  timeout: 300s
bigcartel_product_core:
  address: bigcartel-product-core.dsers-staging.svc.cluster.local:9000
  timeout: 300s
bigcartel_api_proxy:
  address: bigcartel-api-proxy.dsers-staging.svc.cluster.local:9000
  timeout: 300s
dsers_sdk:
  client_id: "1820389489405263872"
  client_secret: qYtnK6Ff6Kk4Gx22sxtjupqFMN3pJDjCBDc0gSfPhwps6eumNPFlcJX05fH2q1w=
  scheme: http
  host: open-dsers-seller-api-gw-staging.topdsers.com
  token_url: http://open-dsers-seller-api-gw-staging.topdsers.com/api/v1/oauth2/token
