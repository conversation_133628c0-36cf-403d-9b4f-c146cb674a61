package data

import (
	"context"
	"fmt"
	"github.com/dsers/infra-nacos-config/internal/biz"
	"os"
	"path/filepath"
	"time"

	"github.com/dsers/infra-kratos-common/pkg/log"
	"github.com/dsers/infra-nacos-config/internal/conf"
	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/go-git/go-git/v5/plumbing/transport"
	"github.com/go-git/go-git/v5/plumbing/transport/http"
)

type gitRepo struct {
	config *conf.GitConfig
	log    *log.Helper
	repo   *git.Repository
}

// NewGitRepo 创建Git仓库
func NewGitRepo(config *conf.GitConfig, logger log.Logger) biz.GitRepo {
	return &gitRepo{
		config: config,
		log:    log.NewHelper(logger),
	}
}

// InitRepository 初始化仓库
func (r *gitRepo) InitRepository(ctx context.Context) error {
	// 检查本地目录是否存在
	if _, err := os.Stat(r.config.Repository.LocalPath); os.IsNotExist(err) {
		// 目录不存在，克隆仓库
		return r.cloneRepository()
	}

	// 目录存在，尝试打开现有仓库
	repo, err := git.PlainOpen(r.config.Repository.LocalPath)
	if err != nil {
		// 如果打开失败，删除目录并重新克隆
		r.log.Warnf("打开现有仓库失败: %v，将重新克隆", err)
		os.RemoveAll(r.config.Repository.LocalPath)
		return r.cloneRepository()
	}

	r.repo = repo
	r.log.Info("成功打开现有Git仓库")
	return nil
}

// cloneRepository 克隆仓库
func (r *gitRepo) cloneRepository() error {
	r.log.Infof("开始克隆仓库: %s", r.config.Repository.URL)

	cloneOptions := &git.CloneOptions{
		URL: r.config.Repository.URL,
		Auth: &http.BasicAuth{
			Username: r.config.Repository.Username,
			Password: r.config.Repository.Token,
		},
		Progress: os.Stdout,
	}

	// 如果配置了代理，添加代理设置
	if r.config.Proxy.URL != "" {
		cloneOptions.ProxyOptions = transport.ProxyOptions{
			URL: r.config.Proxy.URL,
		}
	}

	repo, err := git.PlainClone(r.config.Repository.LocalPath, false, cloneOptions)
	if err != nil {
		return fmt.Errorf("克隆仓库失败: %v", err)
	}

	r.repo = repo
	r.log.Info("成功克隆Git仓库")
	return nil
}

// SaveConfigToFile 保存配置到本地文件
func (r *gitRepo) SaveConfigToFile(ctx context.Context, config conf.ConfigItem) (bool, error) {
	// 创建命名空间对应的目录
	dirPath := filepath.Join(r.config.Repository.LocalPath, config.Namespace)
	err := os.MkdirAll(dirPath, 0755)
	if err != nil {
		return false, fmt.Errorf("创建目录失败: %v", err)
	}

	// 文件名格式为：group+".yaml"
	fileName := config.Group + ".yaml"
	if config.Group == "" {
		fileName = "DEFAULT_GROUP.yaml"
	}
	filePath := filepath.Join(dirPath, fileName)

	// 检查文件是否存在并比对内容
	existingContent, err := os.ReadFile(filePath)
	if err == nil {
		// 文件存在，比对内容
		if string(existingContent) == config.Content {
			r.log.Debugf("配置内容未变化，跳过写入: %s/%s", config.Namespace, fileName)
			return false, nil // 返回false表示没有变化
		}
		r.log.Infof("检测到配置内容变化: %s/%s", config.Namespace, fileName)
	} else if !os.IsNotExist(err) {
		return false, fmt.Errorf("读取现有文件失败: %v", err)
	} else {
		r.log.Infof("新增配置文件: %s/%s", config.Namespace, fileName)
	}

	// 写入文件
	err = os.WriteFile(filePath, []byte(config.Content), 0644)
	if err != nil {
		return false, fmt.Errorf("写入文件失败: %v", err)
	}

	// 更新配置项的文件路径
	config.FilePath = filePath

	r.log.Infof("已保存配置: %s/%s 到文件: %s", config.Namespace, fileName, filePath)
	return true, nil // 返回true表示有变化
}

// CommitAndPush 提交并推送更改
func (r *gitRepo) CommitAndPush(ctx context.Context, message string) error {
	if r.repo == nil {
		return fmt.Errorf("Git仓库未初始化")
	}

	w, err := r.repo.Worktree()
	if err != nil {
		return fmt.Errorf("获取工作树失败: %v", err)
	}

	// 添加所有更改到暂存区
	_, err = w.Add(".")
	if err != nil {
		return fmt.Errorf("添加文件到暂存区失败: %v", err)
	}

	// 检查是否有更改需要提交
	status, err := w.Status()
	if err != nil {
		return fmt.Errorf("获取状态失败: %v", err)
	}

	if status.IsClean() {
		r.log.Info("没有更改需要提交")
		return nil
	}

	// 提交更改
	commit, err := w.Commit(message, &git.CommitOptions{
		Author: &object.Signature{
			Name:  r.config.Commit.AuthorName,
			Email: r.config.Commit.AuthorEmail,
			When:  time.Now(),
		},
	})
	if err != nil {
		return fmt.Errorf("提交失败: %v", err)
	}

	r.log.Infof("成功提交: %s", commit.String())

	// 推送到远程仓库
	pushOptions := &git.PushOptions{
		RemoteName: "origin",
		Auth: &http.BasicAuth{
			Username: r.config.Repository.Username,
			Password: r.config.Repository.Token,
		},
	}

	// 如果配置了代理，添加代理设置
	if r.config.Proxy.URL != "" {
		pushOptions.ProxyOptions = transport.ProxyOptions{
			URL: r.config.Proxy.URL,
		}
	}

	err = r.repo.Push(pushOptions)
	if err != nil {
		return fmt.Errorf("推送失败: %v", err)
	}

	r.log.Info("成功推送到远程仓库")
	return nil
}

// SyncAllConfigs 同步所有配置文件
func (r *gitRepo) SyncAllConfigs(ctx context.Context, configs []conf.ConfigItem) error {
	r.log.Infof("开始同步 %d 个配置文件", len(configs))

	hasChanges := false
	changedCount := 0

	// 保存所有配置文件
	for _, config := range configs {
		changed, err := r.SaveConfigToFile(ctx, config)
		if err != nil {
			r.log.Errorf("保存配置文件失败: %v", err)
			continue
		}
		if changed {
			hasChanges = true
			changedCount++
		}
	}

	// 只有在有变化时才提交并推送
	if hasChanges {
		commitMessage := fmt.Sprintf("同步配置文件 (%d个变更) - %s", changedCount, time.Now().Format("2006-01-02 15:04:05"))
		err := r.CommitAndPush(ctx, commitMessage)
		if err != nil {
			return fmt.Errorf("提交推送失败: %v", err)
		}
		r.log.Infof("配置文件同步完成，共有 %d 个文件发生变化", changedCount)
	} else {
		r.log.Info("配置文件同步完成，没有文件发生变化")
	}

	return nil
}
