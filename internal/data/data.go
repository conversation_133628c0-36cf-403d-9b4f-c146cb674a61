package data

import (
	v1 "github.com/dsers/infra-api-protocol/api/infra-nacos-config/v1"
	"github.com/dsers/infra-kratos-common/pkg/log"
	"github.com/google/wire"
	"google.golang.org/protobuf/types/known/durationpb"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewData,
	NewModelRepo,
	NewNacosRepo,
	NewGitRepo,
	NewNacosConfig,
	NewGitConfig,
	NewConfigSyncConfig,
)

// Data .
type Data struct {
}

func NewData(bootstrap *v1.Bootstrap, logger log.Logger) (*Data, func(), error) {
	// 使用 github.com/dsers/infra-kratos-common/pkg/componet
	// 进行组件的初始化
	// 例如:
	// gorm.NewMySQLWithPanic(logger,bootstrap.MySQL)
	d := &Data{}

	cleanup := func() {
		// 使用 github.com/dsers/infra-kratos-common/pkg/componet
		// 进行组件的资源释放
		// 例如:
		// ctx, cancelFunc := context.WithTimeout(context.Background(), 10*time.Second)
		// defer cancelFunc()
		// gorm.Close(ctx,d.msyql)
	}

	return d, cleanup, nil
}

// NewConfigSyncConfig 创建配置同步配置
func NewConfigSyncConfig(bootstrap *v1.Bootstrap, logger log.Logger) *v1.ConfigSyncConfig {
	helper := log.NewHelper(logger)

	if bootstrap.ConfigSync == nil {
		helper.Warn("配置同步设置为空，使用默认配置")
		// 如果配置为空，返回默认配置
		return &v1.ConfigSyncConfig{
			ScanInterval:   &durationpb.Duration{Seconds: 3600}, // 1小时
			EnableAutoSync: true,
			EnableListener: true,
		}
	}

	// 直接返回bootstrap中的配置（已经是正确的类型）
	helper.Info("配置同步设置创建成功")
	return bootstrap.ConfigSync
}
