package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/dsers/infra-nacos-config/internal/conf"
	"github.com/dsers/infra-nacos-config/internal/data"
	kratoslog "github.com/dsers/infra-kratos-common/pkg/log"
)

func main() {
	// 创建logger
	logger := kratoslog.NewStdLogger(log.Writer())

	// 创建Git配置
	gitConfig := &conf.GitConfig{
		Repository: conf.GitRepositoryConfig{
			URL:       "https://github.com/dsers/nacos-config-yaml.git",
			LocalPath: "./test-nacos-config",
			Username:  "anshuo",
			Token:     "****************************************",
		},
		Proxy: conf.GitProxyConfig{
			URL: "http://127.0.0.1:7890",
		},
		Commit: conf.GitCommitConfig{
			AuthorName:  "Test User",
			AuthorEmail: "<EMAIL>",
		},
	}

	// 创建Git仓库
	gitRepo := data.NewGitRepo(gitConfig, logger)

	ctx := context.Background()

	// 初始化仓库
	fmt.Println("初始化Git仓库...")
	err := gitRepo.InitRepository(ctx)
	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		return
	}

	// 测试配置项
	config1 := conf.ConfigItem{
		DataId:    "application",
		Group:     "DEFAULT_GROUP",
		Content:   "server:\n  port: 8080\nspring:\n  application:\n    name: test-app",
		Namespace: "test",
	}

	config2 := conf.ConfigItem{
		DataId:    "application",
		Group:     "DEFAULT_GROUP",
		Content:   "server:\n  port: 8080\nspring:\n  application:\n    name: test-app", // 相同内容
		Namespace: "test",
	}

	config3 := conf.ConfigItem{
		DataId:    "application",
		Group:     "DEFAULT_GROUP",
		Content:   "server:\n  port: 9090\nspring:\n  application:\n    name: test-app-v2", // 不同内容
		Namespace: "test",
	}

	// 第一次保存配置（应该写入文件）
	fmt.Println("\n=== 第一次保存配置 ===")
	changed, err := gitRepo.SaveConfigToFile(ctx, config1)
	if err != nil {
		fmt.Printf("保存失败: %v\n", err)
		return
	}
	fmt.Printf("文件是否有变化: %v\n", changed)

	// 第二次保存相同配置（应该跳过）
	fmt.Println("\n=== 第二次保存相同配置 ===")
	changed, err = gitRepo.SaveConfigToFile(ctx, config2)
	if err != nil {
		fmt.Printf("保存失败: %v\n", err)
		return
	}
	fmt.Printf("文件是否有变化: %v\n", changed)

	// 第三次保存不同配置（应该写入文件）
	fmt.Println("\n=== 第三次保存不同配置 ===")
	changed, err = gitRepo.SaveConfigToFile(ctx, config3)
	if err != nil {
		fmt.Printf("保存失败: %v\n", err)
		return
	}
	fmt.Printf("文件是否有变化: %v\n", changed)

	// 提交变更
	fmt.Println("\n=== 提交变更 ===")
	err = gitRepo.CommitAndPush(ctx, "测试文件比对功能")
	if err != nil {
		fmt.Printf("提交失败: %v\n", err)
		return
	}

	// 清理测试目录
	fmt.Println("\n=== 清理测试目录 ===")
	os.RemoveAll("./test-nacos-config")

	fmt.Println("文件比对功能测试完成！")
}
