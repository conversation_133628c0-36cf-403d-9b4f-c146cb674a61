// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/dsers/infra-api-protocol/api/common/v1"
	v1_2 "github.com/dsers/infra-api-protocol/api/infra-nacos-config/v1"
	"github.com/dsers/infra-kratos-common/pkg/log"
	server2 "github.com/dsers/infra-kratos-common/pkg/server"
	"github.com/dsers/infra-nacos-config/internal/biz"
	"github.com/dsers/infra-nacos-config/internal/data"
	"github.com/dsers/infra-nacos-config/internal/server"
	"github.com/dsers/infra-nacos-config/internal/service"
	"github.com/go-kratos/kratos/v2"
)

// Injectors from wire.go:

// initApp init kratos application.
func initApp(v1Server *v1.Server, bootstrap *v1_2.Bootstrap, logger log.Logger) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(bootstrap, logger)
	if err != nil {
		return nil, nil, err
	}
	modelRepo := data.NewModelRepo(dataData, logger)
	mainServiceUseCase := biz.NewMainServiceUseCase(modelRepo, logger)
	mainService := service.NewMainService(mainServiceUseCase, logger)
	httpServer := server.NewHTTPServer(bootstrap, mainService, logger)
	grpcServer := server.NewGRPCServer(bootstrap, mainService, logger)
	nacosRepo := data.NewNacosRepo(bootstrap, logger)
	gitRepo := data.NewGitRepo(bootstrap, logger)
	configSyncUseCase := biz.NewConfigSyncUseCase(nacosRepo, gitRepo, bootstrap, logger)
	configSyncService := service.NewConfigSyncService(configSyncUseCase, logger)
	configSyncServer := server.NewConfigSyncServer(configSyncService, logger)
	iServer := server.InjectServer(v1Server)
	profilingServer := server2.NewProfilingServer(iServer, logger)
	metricServer := server2.NewMetricServer(iServer, logger)
	app := newApp(logger, httpServer, grpcServer, configSyncServer, profilingServer, metricServer)
	return app, func() {
		cleanup()
	}, nil
}
