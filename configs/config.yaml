env: local
log:
  level: info
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces

# Nacos配置
nacos:
  server:
    endpoint: "mse-257ca714-nacos-ans.mse.aliyuncs.com:8080"
    host: "mse-257ca714-nacos-ans.mse.aliyuncs.com"
    port: 8848
    context_path: "/nacos"
  client:
    region_id: "cn-<PERSON><PERSON><PERSON><PERSON><PERSON>"
    access_key: "LTAI5tJuh5eLzb8qzGzWapoX"
    secret_key: "******************************"
    open_kms: true
    timeout_ms: 5000
    log_level: "debug"
    log_dir: "./nacos/log"
    cache_dir: "./nacos/cache"
  namespaces:
    prod-app: "0e07a9b4-a03c-4226-85aa-272c36629ee8"
    test: "2df6698b-2dc1-43e4-9b18-5483b5411ec9"
    prod: "48850069-1a83-4996-8293-f5303ec38154"
    staging: "80264b14-a955-4804-a131-07a0b2b6297f"

# Git配置
git:
  repository:
    url: "https://github.com/dsers/nacos-config-yaml.git"
    local_path: "./nacos-config-yaml"
    username: "anshuo"
    token: "****************************************"
  proxy:
    url: "http://127.0.0.1:7890"
  commit:
    author_name: "Nacos Config Sync"
    author_email: "<EMAIL>"

# 配置同步设置
config_sync:
  scan_interval: "3600s"  # 扫描间隔
  enable_auto_sync: true  # 是否启用自动同步
  enable_listener: true   # 是否启用配置监听
