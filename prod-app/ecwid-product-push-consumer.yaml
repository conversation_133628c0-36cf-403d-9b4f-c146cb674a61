# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod-app
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 600s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 600s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 600s
  metric:
    addr: 0.0.0.0:8080
    timeout: 600s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 服务配置
ecwid_product_core:
  address: ecwid-product-core.dsers-app.svc.cluster.local:9000
  timeout: 600s
ecwid_auth_core:
  address: ecwid-auth-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
  # 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw.topdsers.com
  client_id: '1812692082542313472'
  client_secret: wq28EbcDujMTx5w7H5mxZ8EZIHdTLqgq905n_U4tYojEqj-2x3OmbvKb4_IqEl4=
  token_url: http://open-dsers-seller-api-gw.topdsers.com/api/v1/oauth2/token
# push_dispatcher 配置
push_dispatcher:
  name: ecwid-product-push-app
  kafka_consumer:
    consumer_num: 4
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
  id_generator:
    address: center-grpc-api-gw.topdsers.com:9000
    timeout: 20s
  redis:
    addr: r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379
    username: r-8vb7svnsxsrjb1wwze
    password: Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG
    db: 5
    dial_timeout: 5s
  handler_timeout: 600s

# 创建基础商品
push_product_dispatcher:
  name: ecwid-push-product-app
  kafka_consumer:
    consumer_num: 4
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
  id_generator:
    address: center-grpc-api-gw.topdsers.com:9000
    timeout: 20s
  redis:
    addr: r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379
    username: r-8vb7svnsxsrjb1wwze
    password: Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG
    db: 5
    dial_timeout: 5s
  handler_timeout: 600s
# 创建Media
push_media_dispatcher:
  name: ecwid-push-media-app
  kafka_consumer:
    consumer_num: 4
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
  id_generator:
    address: center-grpc-api-gw.topdsers.com:9000
    timeout: 20s
  redis:
    addr: r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379
    username: r-8vb7svnsxsrjb1wwze
    password: Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG
    db: 5
    dial_timeout: 5s
  handler_timeout: 600s
# 创建Variant
push_variant_dispatcher:
  name: ecwid-push-variant-app
  kafka_consumer:
    consumer_num: 4
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
  id_generator:
    address: center-grpc-api-gw.topdsers.com:9000
    timeout: 20s
  redis:
    addr: r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379
    username: r-8vb7svnsxsrjb1wwze
    password: Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG
    db: 5
    dial_timeout: 5s
  handler_timeout: 600s
# 创建Variant检查
push_variant_check_dispatcher:
  name: ecwid-push-variant-check-app
  kafka_consumer:
    consumer_num: 4
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
  id_generator:
    address: center-grpc-api-gw.topdsers.com:9000
    timeout: 20s
  redis:
    addr: r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379
    username: r-8vb7svnsxsrjb1wwze
    password: Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG
    db: 5
    dial_timeout: 5s
  handler_timeout: 600s
# 创建variant图片
push_variant_image_dispatcher:
  name: ecwid-push-variant-image-app
  kafka_consumer:
    consumer_num: 4
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
  id_generator:
    address: center-grpc-api-gw.topdsers.com:9000
    timeout: 20s
  redis:
    addr: r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379
    username: r-8vb7svnsxsrjb1wwze
    password: Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG
    db: 5
    dial_timeout: 5s
  handler_timeout: 600s
# 创建variant图片检查
push_variant_image_check_dispatcher:
  name: ecwid-push-variant-image-check-app
  kafka_consumer:
    consumer_num: 4
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
  id_generator:
    address: center-grpc-api-gw.topdsers.com:9000
    timeout: 20s
  redis:
    addr: r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379
    username: r-8vb7svnsxsrjb1wwze
    password: Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG
    db: 5
    dial_timeout: 5s
  handler_timeout: 600s