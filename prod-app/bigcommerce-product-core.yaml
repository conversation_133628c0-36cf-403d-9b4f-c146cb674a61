env: prod-app
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 300s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 5s
  metric:
    addr: 0.0.0.0:8080
    timeout: 5s
sentry:
  dsn: http://19f047c0280b4ef29b3b051ef0cd5e57@172.16.16.209:9000/21
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 数据库设置
# TODO: 需要向运维部门申请
big_mysql:
  dsn: seller_bigcommerce:5zB6L6jAoJ7rXncpF5VHPYUJD6etR6jf@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_bigcommerce?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
mysql:
  dsn: seller_bigcommerce:5zB6L6jAoJ7rXncpF5VHPYUJD6etR6jf@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_bigcommerce?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
# 应用设置
app_id: 1809125906880790528
# 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw.topdsers.com
  client_id: '1809125906880790528'
  client_secret: taeD-IF4cCqk8UfJRfUOMt3nTk8TGSN1qPY95PXtLkBcOlpO4cdsF6Uq21T-fIU=
  token_url: http://open-dsers-seller-api-gw.topdsers.com/api/v1/oauth2/token
# 服务设置
bigcommerce_api_proxy:
  address: bigcommerce-api-proxy.dsers-app.svc.cluster.local:9000
  timeout: 100s
bigcommerce_auth_core:
  address: bigcommerce-auth-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
# 打点Kafka设置
kafka_collect:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  topic: data-common-collect-server
