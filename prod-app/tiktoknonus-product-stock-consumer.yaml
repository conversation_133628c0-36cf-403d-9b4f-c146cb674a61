# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod-app
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 服务配置
tiktoknonus_product_core:
  address: tiktoknonus-product-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
# dispatcher 配置
dispatcher:
  name: tiktoknonus-product-stock-app
  kafka_consumer:
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: "infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local"
  id_generator:
    address: center-grpc-api-gw.topdsers.com:9000
    timeout: 20s
  redis:
    addr: "r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379"
    username: "r-8vb7svnsxsrjb1wwze"
    password: "Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG"
    db: 5
    dial_timeout: 5s

dsers_app:
  app_id: "1777181304545849344"
  app_name: "tiktoknonus"