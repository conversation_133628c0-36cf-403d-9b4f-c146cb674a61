env: prod-app
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 30s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 30s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 10s
  metric:
    addr: 0.0.0.0:8080
    timeout: 10s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces

redis:
  addr: r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379
  username: r-8vb7svnsxsrjb1wwze
  password: Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG
  db: 3

mysql:
  dsn: temu:HvMkPFGq7XnNRGQZ2LYCzqgt2dFW@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/temu?charset=utf8mb4&parseTime=True&loc=Local&timeout=30s
  debug: false

tmall_currency_core:
  address: tmall-currency-core.dsers-app.svc.cluster.local:9000
  timeout: 25s
tmall_address_core:
  address: tmall-address-core.dsers-app.svc.cluster.local:9000
  timeout: 25s
appId: 159831080

# es配置
#正式
elastic:
 addresses:
   - "http://es-cn-7mz2uydzf0014sapl.elasticsearch.aliyuncs.com:9200"
 username: "elastic"
 password: "GoEnWe3gMLaY2"


dsers_sdk:
  key: "1777182530482438144"
  secret: iFMmLW-OriTzxei4xbV8LurKu8CQFCfHSy0UT0JKBqd7PZnjqSCLeS7Mx8gHcXw=
  token_url: http://open-dsers-supplier-api-gw.topdsers.com/api/v1/oauth2/token
  host: open-dsers-supplier-api-gw.topdsers.com
  schema: http


dsers_sensitive_core:
  address: center-grpc-api-gw.topdsers.com:9000
  timeout: 25s