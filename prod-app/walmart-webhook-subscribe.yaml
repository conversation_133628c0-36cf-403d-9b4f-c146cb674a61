env: prod-app
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 服务配置
walmart_api_proxy:
  address: walmart-api-proxy.dsers-app.svc.cluster.local:9000
  timeout: 100s
walmart_auth_core:
  address: walmart-auth-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
# callback_url hook接收地址
callback_url: https://open-seller-api-gw.dsers.com/walmart/walmart-webhook-consumer/webhook
# ants_pool 协程数
ants_pool: 10
# kafka配置
kafka:
  brokers:
    - alikafka-pre-cn-nwy3l013m007-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-3-vpc.alikafka.aliyuncs.com:9092
  consumer_num: 2
  topic: walmart-webhook-subscription
  group_id: walmart-webhook-subscription-consumer

events:
  - type: INVENTORY_OOS
    version: V1
    resource_name: INVENTORY
  - type: BUY_BOX_CHANGED
    version: V1
    resource_name: PRICE
  - type: OFFER_PUBLISHED
    version: V1
    resource_name: ITEM
  - type: OFFER_UNPUBLISHED
    version: V1
    resource_name: ITEM
  - type: DRIVER_STATUS
    version: V1
    resource_name: ORDER
  - type: DSV_CARRIER_EDD_UPDATE
    version: V1
    resource_name: ORDER
  - type: INTENT_TO_CANCEL
    version: V1
    resource_name: ORDER
  - type: MCS_RETURN_ORDER_STATUS_UPDATE
    version: V1
    resource_name: ORDER
  - type: ORDER_SHIPMENT_UPDATE
    version: V1
    resource_name: ORDER
  - type: ORDER_STATUS_UPDATE
    version: V1
    resource_name: ORDER
  - type: PO_CREATED
    version: V1
    resource_name: ORDER
  - type: PO_LINE_AUTOCANCELLED
    version: V1
    resource_name: ORDER