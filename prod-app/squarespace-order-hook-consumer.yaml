
# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod-app
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 10s
  metric:
    addr: 0.0.0.0:8080
    timeout: 10s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# hook kafka配置
kafka:
  brokers:
    - alikafka-pre-cn-nwy3l013m007-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-3-vpc.alikafka.aliyuncs.com:9092
  # 消费者数量
  consumer_num: 1
  topic: squarespace-order-hook
  group_id: squarespace-order-hook-consumer
# 服务配置
squarespace_order_core:
  address: squarespace-order-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
