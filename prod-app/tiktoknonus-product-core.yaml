# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod-app
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 300s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 300s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 300s
  metric:
    addr: 0.0.0.0:8080
    timeout: 300s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 数据库设置
mysql:
  dsn: seller_tiktoknonus:6YwmHCUfd9MUuhKqDm8gTp3MXMVA@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_tiktoknonus?charset=utf8mb4&parseTime=True&loc=Local
  debug: false
# 应用设置
app_id: 1777181304545849344
# 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw.topdsers.com
  client_id: "1777181304545849344"
  client_secret: u7vevGzodExmL0JQuTV40ysDdiF-zavV-lBmhWMuxCQQf3c7k8aLbe8Rz6DOqlo=
  token_url: http://open-dsers-seller-api-gw.topdsers.com/api/v1/oauth2/token
# 服务设置
tiktoknonus_api_proxy:
  address: tiktoknonus-api-proxy.dsers-app.svc.cluster.local:9000
  timeout: 300s
tiktoknonus_auth_core:
  address: tiktoknonus-auth-core.dsers-app.svc.cluster.local:9000
  timeout: 300s
tmall_currency_core:
  address: tmall-currency-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
dsers_product_admin:
  address: center-grpc-api-gw.topdsers.com:9000
  timeout: 100s
# hook kafka配置
kafka:
  addrs:
    - alikafka-pre-cn-nwy3l013m007-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-3-vpc.alikafka.aliyuncs.com:9092
  # 消费者数量
  consumer_num: 2
  mock_topic: tiktoknonus-product-mock-hook

# redis配置
redis:
  addr: "r-8vb6rjtjj55hwggyny.redis.zhangbei.rds.aliyuncs.com:6379"
  username: "r-8vb6rjtjj55hwggyny"
  password: "9Ammg7fJOwzDU"
  db: 0
