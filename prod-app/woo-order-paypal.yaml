env: prod-app
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
mysql:
  dsn: woo:ioboofah5eu7Baituc2ciutahM0ahraV@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/woo?charset=utf8mb4&parseTime=True&loc=Local
  debug: false

order_paypal_dispatcher:
  name: woo-order-paypal
  kafka_consumer:
    consumer_num: 4
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
  id_generator:
    address: center-grpc-api-gw.topdsers.com:9000
    timeout: 20s
  redis:
    addr: r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379
    username: r-8vb7svnsxsrjb1wwze
    password: Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG
    db: 5
    dial_timeout: 5s
dsers_pay_gateway:
  address: "center-grpc-api-gw.topdsers.com:9000"
  timeout: 10s
