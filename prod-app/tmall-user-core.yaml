env: prod-app
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s

# dsers配置
dsers:
  client_id: ZaoMaiHaeph6uMokiBoo
  client_secret: mei1iZ2elieB8rubeequephie4de7aihie3ro1ophaifi1mahl
  token_url: "http://open-dsers-supplier-api-gw.topdsers.com/api/v1/oauth2/token"
  host: "open-dsers-supplier-api-gw.topdsers.com"
  scheme: http

# mysql配置
mysql:
  dsn: "supplier_tmall:ahro1ohF4shiebaedeu6shohXaefohzi@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/supplier_tmall?charset=utf8mb4&parseTime=True&loc=Local"

# 天猫配置
tmall:
  # 分销商商家id
  seller_id: "2100004404487"
  app_key: "500654"
  app_secret: "sbNFZTsxXZUHdtoG7hszaYind1Csq9K3"
