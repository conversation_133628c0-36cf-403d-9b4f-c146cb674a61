# Nacos配置同步到Git功能

## 功能概述

这个项目实现了自动将Nacos配置中心的配置文件同步到Git仓库的功能，包括：

1. **项目启动时全量同步**：项目启动时自动拉取所有配置文件并提交到Git仓库
2. **实时配置监听**：监听Nacos配置变化，当配置发生变动时自动更新文件并提交
3. **定时全量扫描**：每小时扫描一次全部配置文件，检查是否有新增配置并同步
4. **智能文件比对**：比对文件内容，只有在内容发生变化时才写入文件和提交，避免无意义的提交

## 架构设计

项目采用DDD（领域驱动设计）分层架构：

```
cmd/server/          # 应用启动入口
├── main.go         # 主程序
└── wire.go         # 依赖注入配置

internal/
├── conf/           # 配置结构定义
├── data/           # 数据访问层
│   ├── nacos.go    # Nacos数据访问
│   └── git.go      # Git数据访问
├── biz/            # 业务逻辑层
│   └── config_sync.go  # 配置同步业务逻辑
├── service/        # 服务层
│   └── config_sync_service.go  # 配置同步服务
└── server/         # 服务器层
    └── config_sync.go  # 配置同步服务器
```

## 核心组件

### 1. ConfigSyncServer (服务器层)
- 作为独立的服务器组件，在项目启动时自动启动
- 负责管理配置同步服务的生命周期
- 实现了Kratos的transport.Server接口

### 2. ConfigSyncService (服务层)
- 提供配置同步的服务接口
- 封装业务逻辑，提供统一的服务调用

### 3. ConfigSyncUseCase (业务逻辑层)
- 实现核心的配置同步逻辑
- 管理定时任务和配置监听
- 协调Nacos和Git的操作

### 4. NacosRepo & GitRepo (数据访问层)
- NacosRepo: 负责与Nacos配置中心交互
- GitRepo: 负责Git仓库操作

## 配置说明

在 `configs/config.yaml` 中配置相关参数：

```yaml
# Nacos配置
nacos:
  server:
    endpoint: "mse-257ca714-nacos-ans.mse.aliyuncs.com:8080"
    host: "mse-257ca714-nacos-ans.mse.aliyuncs.com"
    port: 8848
    context_path: "/nacos"
  client:
    region_id: "cn-zhangjiakou"
    access_key: "your-access-key"
    secret_key: "your-secret-key"
    open_kms: true
    timeout_ms: 5000
    log_level: "debug"
    log_dir: "./nacos/log"
    cache_dir: "./nacos/cache"
  namespaces:
    prod-app: "namespace-id-1"
    test: "namespace-id-2"
    prod: "namespace-id-3"
    staging: "namespace-id-4"

# Git配置
git:
  repository:
    url: "https://github.com/your-org/nacos-config-yaml.git"
    local_path: "./nacos-config-yaml"
    username: "your-username"
    token: "your-github-token"
  proxy:
    url: "http://127.0.0.1:7890"  # 可选，如果需要代理
  commit:
    author_name: "Nacos Config Sync"
    author_email: "<EMAIL>"

# 配置同步设置
config_sync:
  scan_interval: "1h"  # 扫描间隔
  enable_auto_sync: true  # 是否启用自动同步
  enable_listener: true   # 是否启用配置监听
```

## 使用方法

### 1. 启动项目

```bash
# 构建项目
make build

# 启动服务
./bin/server -conf configs/config.yaml
```

### 2. 自动同步

项目启动后，ConfigSyncServer会自动：
- 初始化Git仓库（克隆或打开现有仓库）
- 执行一次全量同步，拉取所有Nacos配置
- 启动配置变化监听
- 启动定时扫描任务（默认每小时一次）

### 3. 文件组织结构

同步后的文件会按以下结构组织：

```
nacos-config-yaml/
├── prod-app/
│   ├── application.yaml
│   ├── database.yaml
│   └── redis.yaml
├── test/
│   ├── application.yaml
│   └── database.yaml
├── prod/
│   └── application.yaml
└── staging/
    └── application.yaml
```

## 智能文件比对功能

### 文件内容比对机制
- **读取现有文件**：在写入新配置前，先读取本地文件的现有内容
- **内容比较**：将Nacos配置内容与本地文件内容进行字符串比较
- **智能跳过**：如果内容完全相同，跳过文件写入和Git提交操作
- **变化检测**：只有在内容发生变化时才执行写入和提交

### 优势
- **减少无意义提交**：避免内容相同的重复提交，保持Git历史清洁
- **提高性能**：减少不必要的文件I/O和Git操作
- **节省存储**：避免重复的Git对象存储
- **清晰的变更记录**：Git提交历史只包含真正的配置变更

## 工作流程

### 1. 启动时全量同步
1. 项目启动时，ConfigSyncServer自动启动
2. 初始化Git仓库（克隆或打开）
3. 从所有配置的命名空间获取配置文件
4. **比对每个配置文件内容**，只保存有变化的文件
5. 如果有文件变化，提交并推送到Git仓库

### 2. 实时配置监听
1. 为每个命名空间的每个配置添加监听器
2. 当配置发生变化时，触发回调函数
3. **比对配置内容**，只有在内容真正变化时才更新文件
4. 如果有变化，提交并推送变更到Git仓库

### 3. 定时全量扫描
1. 每小时执行一次全量扫描
2. 获取所有命名空间的最新配置
3. **与本地文件逐一比对**，发现新增或变更的配置
4. 只同步有变化的配置并提交到Git仓库

## 测试

可以使用提供的测试脚本进行功能验证：

```bash
go run test_config_sync.go
```

## 注意事项

1. **安全性**：确保Git token和Nacos访问密钥的安全性
2. **网络**：如果在受限网络环境中，可能需要配置代理
3. **权限**：确保Git token有足够的权限进行推送操作
4. **存储**：定期清理本地缓存和日志文件
5. **监控**：建议添加监控和告警，及时发现同步异常

## 扩展功能

未来可以考虑添加：
- Web管理界面
- 配置变更历史查看
- 多Git仓库支持
- 配置文件格式转换
- 更细粒度的同步控制

---

## 文档署名

本文档由 **Augment Agent** (基于 Claude Sonnet 4) 协助编写和设计。

Augment Agent 是由 Augment Code 开发的智能编程助手，专注于提供高质量的代码架构设计、实现方案和技术文档。

- **AI模型**: Claude Sonnet 4 by Anthropic
- **开发商**: Augment Code
- **生成时间**: 2025年7月3日
- **版本**: v1.0

如有技术问题或改进建议，欢迎反馈交流。
