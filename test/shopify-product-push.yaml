env: test
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://19f047c0280b4ef29b3b051ef0cd5e57@172.16.16.209:9000/21
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
mysql:
  dsn: seller_shopify_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_shopify_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true

product_mysql:
  dsn: seller_shopify:m47Xzg8dB2qyQkPM@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_shopify?charset=utf8mb4&parseTime=True&loc=Local
  debug: true

kafka:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  push_img_topic: shopify-product-push-img
  old_push_topic: shopify-product-push
  old_push_group_id: shopify-product-push-consumer
  event_push_topic: shopify-product-event-push
  event_push_group_id: shopify-product-event-push-consumer
  replace_img_topic: shopify-product-replace-img
  replace_img_group_id: shopify-product-replace-img-consumer
  consumer_num: 4

open_api:
  client_id: iyie8Osoodoo5aih2ieJ
  client_secret: mei1iZ2elieB8rubeequephie4de7aihie3ro1ophaifi1mahl
  token_url: http://open-dsers-seller-api-gw-test.topdsers.com/api/v1/oauth2/token
  scheme: http
  host: open-dsers-seller-api-gw-test.topdsers.com
shopify_api_proxy:
  address: "shopify-api-proxy.dsers-test.svc.cluster.local:9000"
  timeout: 100s
shopify_user_core:
  address: "shopify-user-core.dsers-test.svc.cluster.local:9000"
  timout: 50s  