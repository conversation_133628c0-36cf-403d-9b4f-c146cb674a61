# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: test
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 服务配置
tiktok_product_core:
  address: tiktok-product-core.dsers-test.svc.cluster.local:9000
  timeout: 100s
tmall_currency_core:
  address: tmall-currency-core.dsers-test.svc.cluster.local:9000
  timeout: 100s
# dispatcher 配置
dispatcher:
  name: tiktok-product-price
  kafka_consumer:
    brokers:
      - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: false
  id_generator:
    address: infra-id-generator.dsers-test.svc.cluster.local:9000
    timeout: 100s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5

# 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw-test.topdsers.com
  client_id: '1760860516168114176'
  client_secret: rmyvqMSNBp0Wq4814Q2BSLhFT4jabwhqyZbqNIAvG6tyzhH7TNCAkAAqFw7kGpk=
  token_url: http://open-dsers-seller-api-gw-test.topdsers.com/api/v1/oauth2/token
