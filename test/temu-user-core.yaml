env: test
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 15s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 30s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces


databases:
  temu:
    dsn: temu:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/temu?charset=utf8mb4&parseTime=True&loc=Local
    debug: true

dsers_app:
  client_id: "1764581412021874688"
  client_secret: Fv-NrICbLj2Y2xyw-Ulic-IVBzO17e-Ecycb4pvlNhXlOn18kD2DAGMD4ZHgBwI=
  scheme: http
  host: open-dsers-supplier-api-gw-test.topdsers.com
  token_url: /api/v1/oauth2/token

grpc_clients:
  dsers_user_supplier:
    address: dsers-user-supplier.dsers-test.svc.cluster.local:9000
    timeout: 30s
  dsers_user_supplier_auth:
    address: dsers-user-supplier-auth.dsers-test.svc.cluster.local:9000
    timeout: 30s

dsers_redirect_url:
  login_url: https://dev.dsers.com/application/
  auth_url: https://dev.dsers.com/application/auth_check
