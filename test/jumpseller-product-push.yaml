server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://19f047c0280b4ef29b3b051ef0cd5e57@172.16.16.209:9000/21
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
mysql:
  dsn: jumpseller_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/jumpseller_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true
kafka:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
open_api:
  client_id: "1656559719228796928"
  client_secret: hHjbUR_N7zQKMjpVBorL__L-611jFXyJ3JnGplEhzxmZYLj40Q1zOP-s9giHJso=
  token_url: http://open-dsers-seller-api-gw-test.topdsers.com/api/v1/oauth2/token
  scheme: http
  host: open-dsers-seller-api-gw-test.topdsers.com
jumpseller_api_proxy:
  address: "jumpseller-api-proxy.dsers-test.svc.cluster.local:9000"
  timeout: 100s