
# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: test
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 300s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 300s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 300s
  metric:
    addr: 0.0.0.0:8080
    timeout: 300s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# Kafka设置
kafka:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  consumer_num: 2
  order_topic: magento-order-hook
  product_topic: magento-product-hook
  app_topic: magento-app-hook
# 服务设置
magento_auth_core:
  address: magento-auth-core.dsers-test.svc.cluster.local:9000
  timeout: 100s
magento_product_core:
  address: magento-product-core.dsers-test.svc.cluster.local:9000
  timeout: 100s
magento_order_core:
  address: magento-order-core.dsers-test.svc.cluster.local:9000
  timeout: 100s
