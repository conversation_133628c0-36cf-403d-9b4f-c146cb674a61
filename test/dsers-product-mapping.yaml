server:
  http:
    addr: 0.0.0.0:8000
    timeout: 30s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 200s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 30s
  metric:
    addr: 0.0.0.0:8080
    timeout: 30s
mysql:
  dsn: dsers_product_mapping_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/dsers_product_mapping_new?loc=Local&parseTime=true&charset=utf8mb4
mysql_core:
  dsn: dsers_product_core_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/dsers_product_core_new?loc=Local&parseTime=true&charset=utf8mb4

redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73o
cache_redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73o
  db: 10
dsers_product_supplier:
  address: dsers-product-supplier.dsers-test.svc.cluster.local:9000
  timeout: 50s
dsers_product_core:
  address: "dsers-product-core.dsers-test.svc.cluster.local:9000"
  timeout: 50s
dsers_user_core:
  address: dsers-user-core.dsers-test.svc.cluster.local:9000
  timeout: 25s
open_app_core:
  address: open-app-core.dsers-test.svc.cluster.local:9000
  timeout: 50s
dsers_product_seller:
  address: dsers-product-seller.dsers-test.svc.cluster.local:9000
  timeout: 50s
dsers_gemini_vertexai:
  address: dsers-gemini-vertexai.dsers-test.svc.cluster.local:9000
  timeout: 50s

kafka:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092


orderjob_consumer_kafka:
  brokers:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  group_id: dsers_product_mapping
  topic: dsers-order-mapping-job
  consumer_num: 2

producer_mapping_changed_topic: dsers-product-mapping-changed
producer_app_changed_topic: dsers-product-mapping-app-changed

# order处理完成Mapping回调通知消息处理
consumer_mapping_processed_topic: "dsers-order-mapping-job"
consumer_mapping_processed_group: "dsers_product_mapping"

# 商品mapping缓存超时时间
product_mapping_timeout: 300s

dsers_product_search:
  address: dsers-product-search.dsers-test.svc.cluster.local:9000
  timeout: 20s
bedrock_config:
  app_key: "********************"
  app_secrect: "Ulovu0FBCSZ6+bySIuHWnUlxwC8/5et0VYlos5oW"
  proxy: "http://************:3128"
embedding_config:
  url: "https://api-gw-test.dsers.com/infra-imgsearch-core/get-embedding"
  method: "POST"
# 供应商sdk配置
supplier_sdk:
  host: open-app-proxy.dsers-test.svc.cluster.local:8000
  scheme: http
# 销售商sdk配置
seller_sdk:
  host: open-app-proxy.dsers-test.svc.cluster.local:8000
  scheme: http
dsers_user_seller:
  address: dsers-user-seller.dsers-test.svc.cluster.local:9000
  timeout: 50s
aliexpress_product_core:
  address: aliexpress-product-core.dsers-test.svc.cluster.local:9000
  timeout: 50s
tiktoknonus_product_core:
  address: tiktoknonus-product-core.dsers-test.svc.cluster.local:9000
  timeout: 50s
collect_producer:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
  topics:
    - data-common-collect-server
user_op_kafka:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092
ali_1688_app_id: 1902659021782450176
ae_app_id: 159831080

dsers_order_search:
  address: dsers-order-search.dsers-test.svc.cluster.local:9000
  timeout: 50s
