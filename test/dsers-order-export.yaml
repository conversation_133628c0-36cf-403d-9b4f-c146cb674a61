env: prod
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 10s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 10s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 10s
  metric:
    addr: 0.0.0.0:8080
    timeout: 10s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces

oss_config:
  url: https://oss-cn-zhangjiakou.aliyuncs.com
  bucket: dsers-order-export-test

order_core:
  address: dsers-order-core.dsers-test.svc.cluster.local:9000
  timeout: 30s

mysql:
  dsn: dsers_order_core_new:idaejiethoowaiwoiV8P@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/dsers_order_core_new?loc=Local&parseTime=true&charset=utf8mb4
  debug: true

redis:
  addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
  username: default
  password: 7LVx4gQlng73og
  db: 0

kafka:
  addrs:
    - alikafka-pre-cn-7pp2vexz4003-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-7pp2vexz4003-3-vpc.alikafka.aliyuncs.com:9092

oss:
  endpoint: oss-cn-zhangjiakou.aliyuncs.com
  sts_token_client:
    address: infra-sts-token.dsers-test.svc.cluster.local:9000
    timeout: 10s
  bucket_name: dsers-order-export-test

search_cli:
  address: dsers-order-search.dsers-test.svc.cluster.local:9000
  timeout: 30s

user_cli:
  address: dsers-user-core.dsers-test.svc.cluster.local:9000
  timeout: 30s

setting_cli:
  address: dsers-settings-order.dsers-test.svc.cluster.local:9000
  timeout: 30s

currency_cli:
  address: tmall-currency-core.dsers-test.svc.cluster.local:9000
  timeout: 30s

open_app_cli:
  address: open-app-core.dsers-test.svc.cluster.local:9000
  timeout: 30s

dsers_user_supplier:
  address: dsers-user-supplier.dsers-test.svc.cluster.local:9000
  timeout: 60s
