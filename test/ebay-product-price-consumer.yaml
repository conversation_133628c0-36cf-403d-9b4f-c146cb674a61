
# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: test
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 300s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 数据库设置
# TODO: 需要向运维部门申请
mysql:
  dsn: seller_ebay:eR3En8FRRqaRpqQYoedCf7HyRdoq@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_ebay?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
dispatcher:
  name: ebay-product-price-test
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:BVkZJZcR8oEPnaR7g2f8PstTDbNB@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-test.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
    dial_timeout: 5s
ebay_auth_core:
  address: ebay-auth-core.dsers-test.svc.cluster.local:9000
  timeout: 20s
ebay_product_core:
  address: ebay-product-core.dsers-test.svc.cluster.local:9000
  timeout: 20s
ebay_api_proxy:
  address: ebay-api-proxy.dsers-test.svc.cluster.local:9000
  timeout: 20s
dsers_sdk:
  client_id: "1808062237104406528"
  client_secret: mikl-_ZV0AHibyVroZyGp8Id44lLRAZrGGJZ0UsWMj_G7IqiXddElNBmazSQpjo=
  scheme: http
  host: open-dsers-seller-api-gw-test.topdsers.com
  token_url: http://open-dsers-seller-api-gw-test.topdsers.com/api/v1/oauth2/token