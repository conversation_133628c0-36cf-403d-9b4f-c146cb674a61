---
# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 服务配置
tiktoknonus_api_proxy:
  address: tiktoknonus-api-proxy.dsers-app.svc.cluster.local:9000
  timeout: 300s
tiktoknonus_order_core:
  address: tiktoknonus-order-core.dsers-app.svc.cluster.local:9000
  timeout: 300s
# 数据库设置
mysql:
  dsn: seller_tiktoknonus:6YwmHCUfd9MUuhKqDm8gTp3MXMVA@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_tiktoknonus?charset=utf8mb4&parseTime=True&loc=Local
  debug: false
# dsers
dsers_redirect_url:
  login_url: https://www.dsers.com/application/
  auth_url: https://www.dsers.com/application/auth_check
# dsers app
dsers_app:
  client_id: "1777181304545849344"
  client_secret: u7vevGzodExmL0JQuTV40ysDdiF-zavV-lBmhWMuxCQQf3c7k8aLbe8Rz6DOqlo=
  app_id: "1777181304545849344"
  scheme: http
  host: open-dsers-seller-api-gw.topdsers.com
  token_url: /api/v1/oauth2/token

# tiktok app
tiktoknonus:
  api_key: 6bk3v2cor1qse
  api_secret: 7818749888d5aca93586fe2e4ad9b7190dd37fdd
  api_version: "202309"
  auth_url: https://seller-uk.tiktok.com/services/market/custom-authorize
  app_id: "7337129568270599941"
  host: "open-seller-api-gw.dsers.com"

# Kafka
kafka:
  addrs:
    - alikafka-pre-cn-nwy3l013m007-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-3-vpc.alikafka.aliyuncs.com:9092
  consumer_num: 1
  group_id: tiktoknonus-app-hook-consumer
  topic: tiktoknonus-app-hook


