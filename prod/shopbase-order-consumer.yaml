# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 服务配置
shopbase_order_core:
  address: shopbase-order-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
# fulfill_event_dispatcher 配置
fulfill_event_dispatcher:
  name: shopbase-fulfill-event
  kafka_consumer:
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: "infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local"
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: "r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379"
    username: "r-8vb7svnsxsrjb1wwze"
    password: "Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG"
    db: 5
    dial_timeout: 5s
# fulfill_create_dispatcher 配置
fulfill_create_dispatcher:
  name: shopbase-fulfill-create
  kafka_consumer:
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: "infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local"
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: "r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379"
    username: "r-8vb7svnsxsrjb1wwze"
    password: "Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG"
    db: 5
    dial_timeout: 5s
# fulfill_update_dispatcher 配置
fulfill_update_dispatcher:
  name: shopbase-fulfill-update
  kafka_consumer:
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: "infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local"
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: "r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379"
    username: "r-8vb7svnsxsrjb1wwze"
    password: "Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG"
    db: 5
    dial_timeout: 5s
# paypal_dispatcher 配置
paypal_dispatcher:
  name: shopbase-order-paypal
  kafka_consumer:
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: "infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local"
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: "r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379"
    username: "r-8vb7svnsxsrjb1wwze"
    password: "Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG"
    db: 5
    dial_timeout: 5s
# rollback_dispatcher 配置
rollback_dispatcher:
  name: shopbase-order-rollback
  kafka_consumer:
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: "infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local"
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: "r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379"
    username: "r-8vb7svnsxsrjb1wwze"
    password: "Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG"
    db: 5
    dial_timeout: 5s
# update_note_dispatcher 配置
update_note_dispatcher:
  name: shopbase-order-updatenote
  kafka_consumer:
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: "infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local"
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: "r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379"
    username: "r-8vb7svnsxsrjb1wwze"
    password: "Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG"
    db: 5
    dial_timeout: 5s
# 应用设置
app_id: "1821389191666991104"
