server:
  http:
    addr: 0.0.0.0:8000
    timeout: 50s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 50s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://19f047c0280b4ef29b3b051ef0cd5e57@172.16.16.209:9000/21
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
mysql:
    dsn: seller_shopify:Dwp7gN3Ksq3A2HTKzY3bNn4AHdpH@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_shopify?charset=utf8mb4&parseTime=True&loc=Local
    debug: false
services:
  shopify-api-proxy: shopify-api-proxy.dsers-app.svc.cluster.local:9000

kafka:
  addrs:
    - alikafka-pre-cn-zvp2uypcn00d-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-zvp2uypcn00d-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-zvp2uypcn00d-3-vpc.alikafka.aliyuncs.com:9092
open_api:
  client_id: iyie8Osoodoo5aih2ieJ
  client_secret: mei1iZ2elieB8rubeequephie4de7aihie3ro1ophaifi1mahl
  token_url: http://open-dsers-seller-api-gw.topdsers.com/api/v1/oauth2/token
  scheme: http
  host: open-dsers-seller-api-gw.topdsers.com

shopify_user_core:
  address: "shopify-user-core.dsers-app.svc.cluster.local:9000"
  timout: 50s  