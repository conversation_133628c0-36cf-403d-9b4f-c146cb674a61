env: prod
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s

grpc_clients:
  woo_api_sdk:
    address: woo-api-sdk.dsers-app.svc.cluster.local:9000
    timeout: 300s
  woo_trans_core:
    address: woo-trans-core.dsers-app.svc.cluster.local:9000
    timeout: 300s
  dsers_user_core:
    timeout: 300s
    address: dsers-user-core.dsers-app.svc.cluster.local:9000
  dsers_user_seller:
    timeout: 30s
    address: dsers-user-seller.dsers-app.svc.cluster.local:9000


config_urls:
  dsers_login_url: https://www.dsers.com/application/
  dsers_auth_url: https://www.dsers.com/application/auth_check
  app_landpage_auth_url: https://www.dsers.com/application/woo_authentication
  app_landpage_reauth_url: https://www.dsers.com/application/woo_reauthorize
  woo_return_url: https://www.dsers.com/application/woo_callback
  woo_callback_url: https://woo-api-gw.dsers.com/woo-user-bff/woo/callback
  app_dsers_redirect_url: https://woo-api-gw.dsers.com/woo-user-bff/application/redirect
  app_hook_prefix_url: https://woo-api-gw.dsers.com/woo-user-bff/hook/
  
dsers_app:
  client_id: "1656559815622291456"
  client_secret: qeyiTACmDYETsMOlYkzWgYabeUv5cLYQNvxBjtZHL-34-O5ng6j5UMAVZoaTDDQ=
  scheme: http
  host: open-dsers-seller-api-gw.topdsers.com
  token_url: /api/v1/oauth2/token

kafka:
  addrs:
    - alikafka-pre-cn-zvp2uypcn00d-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-zvp2uypcn00d-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-zvp2uypcn00d-3-vpc.alikafka.aliyuncs.com:9092

app_id: "1656559815622291456"
