---
# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 5s
  metric:
    addr: 0.0.0.0:8080
    timeout: 5s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@*************:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 服务配置
bigcommerce_order_core:
  address: bigcommerce-order-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
# fulfill_event_dispatcher 配置
fulfill_event_dispatcher:
  name: dispatch-bigcommerce-fulfill-event-job
  kafka_consumer:
    brokers:
      - alikafka-pre-cn-nwy3l013m007-1-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-nwy3l013m007-2-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-nwy3l013m007-3-vpc.alikafka.aliyuncs.com:9092
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: false
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 100s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
# fulfill_create_dispatcher 配置
fulfill_create_dispatcher:
  name: dispatch-bigcommerce-fulfill-create-job
  kafka_consumer:
    brokers:
      - alikafka-pre-cn-nwy3l013m007-1-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-nwy3l013m007-2-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-nwy3l013m007-3-vpc.alikafka.aliyuncs.com:9092
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: false
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 100s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
# fulfill_update_dispatcher 配置
fulfill_update_dispatcher:
  name: dispatch-bigcommerce-fulfill-update-job
  kafka_consumer:
    brokers:
      - alikafka-pre-cn-nwy3l013m007-1-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-nwy3l013m007-2-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-nwy3l013m007-3-vpc.alikafka.aliyuncs.com:9092
    consumer_num: 2
    concurrent_num: 10
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: false
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 100s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    password: 7LVx4gQlng73o
    db: 5
bigcommerce_api_proxy:
  address: bigcommerce-api-proxy.dsers-app.svc.cluster.local:9000
  timeout: 60s
mysql:
  dsn: seller_bigcommerce:5zB6L6jAoJ7rXncpF5VHPYUJD6etR6jf@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_bigcommerce?charset=utf8mb4&parseTime=True&loc=Local
  debug: true