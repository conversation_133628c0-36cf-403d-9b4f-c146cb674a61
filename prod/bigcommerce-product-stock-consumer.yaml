
# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 300s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 1s
  metric:
    addr: 0.0.0.0:8080
    timeout: 1s
sentry:
  dsn: http://19f047c0280b4ef29b3b051ef0cd5e57@172.16.16.209:9000/21
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 数据库设置
# TODO: 需要向运维部门申请
mysql:
  dsn: seller_bigcommerce:hS5XkxMj9MTSVcoy7xV5EHS7uvnq@tcp(pc-8vb50i1q87w34j629.rwlb.zhangbei.rds.aliyuncs.com:3306)/seller_bigcommerce?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
dispatcher:
  name: dispatch-bigcommerce-product-stock-job
  kafka_consumer:
    brokers:
      - alikafka-pre-cn-nwy3l013m007-1-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-nwy3l013m007-2-vpc.alikafka.aliyuncs.com:9092
      - alikafka-pre-cn-nwy3l013m007-3-vpc.alikafka.aliyuncs.com:9092
    consumer_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
    debug: true
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 50s
  redis:
    addr: r-8vb92lwncsgbu4bd8o.redis.zhangbei.rds.aliyuncs.com:6379
    username: default
    password: 7LVx4gQlng73o
    db: 1
bigcommerce_auth_core:
  address: bigcommerce-auth-core.dsers-app.svc.cluster.local:9000
  timeout: 20s
bigcommerce_product_core:
  address: bigcommerce-product-core.dsers-app.svc.cluster.local:9000
  timeout: 20s
bigcommerce_api_proxy:
  address: bigcommerce-api-proxy.dsers-app.svc.cluster.local:9000
  #  address: localhost:9000
  timeout: 20s