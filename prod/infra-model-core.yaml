client:
  sts_token:
    address: infra-sts-token.dsers-app.svc.cluster.local:9000
    timeout: 20s
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 20s

database:
  model_core:
    dsn: "infra_model_core:nLPGixGR3V7vIt@tcp(pc-8vbp8jp9csr86826k.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/infra_model_core?loc=Local&parseTime=true&charset=utf8mb4"
    debug: true

redis:
  lock:
    addr: r-8vb4jvl92xg29yn56c.redis.zhangbei.rds.aliyuncs.com:6379
    username: r-8vb4jvl92xg29yn56c
    password: 9YASTb7dQuvJ
    db: 1
    pool_size: 100
    min_idle_conns: 100

internal_address:
  serving_http: ************:8501
  serving_rpc: ************:8500
  mserver_http: ************:38888

flagr:
  address: flagr.flagr-system.svc.cluster.local:18000
  flag_id: 11
  flag_key: kb9i87uzhhpug6ra3

# 基于Docker容器的路径
model_base_path: "/models"

feishu_bot:
  address: https://open.feishu.cn/open-apis/bot/v2/hook/9db2fdea-6b3d-4c29-a175-7ba13adfe326
  sign: RIGtjsW7aQsHDc8dI5JgDh

ads_internal_address:
  serving_http: ************:8601
  serving_rpc: ************:8600
  mserver_http: ************:38888

ads_flagr:
  address: flagr.flagr-system.svc.cluster.local:18000
  flag_id: 12
  flag_key: kib1sjvrkvutkyihx

