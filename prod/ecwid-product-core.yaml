# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 600s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 600s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 600s
  metric:
    addr: 0.0.0.0:8080
    timeout: 600s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 数据库设置
big_mysql:
  dsn: seller_ecwid:fJ6zUDRVStPpSRK7h58t7foBFHNbDUbh@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_ecwid?charset=utf8mb4&parseTime=True&loc=Local
  debug: false
mysql:
  dsn: seller_ecwid:fJ6zUDRVStPpSRK7h58t7foBFHNbDUbh@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_ecwid?charset=utf8mb4&parseTime=True&loc=Local
  debug: false
# 应用设置
app_id: "1812692082542313472"
# 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw.topdsers.com
  client_id: "1812692082542313472"
  client_secret: wq28EbcDujMTx5w7H5mxZ8EZIHdTLqgq905n_U4tYojEqj-2x3OmbvKb4_IqEl4=
  token_url: http://open-dsers-seller-api-gw.topdsers.com/api/v1/oauth2/token
# 服务设置
ecwid_auth_core:
  address: ecwid-auth-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
ecwid_api_proxy:
  address: ecwid-api-proxy.dsers-app.svc.cluster.local:9000
  timeout: 600s
# 打点Kafka设置
kafka_collect:
  addrs:
    - alikafka-pre-cn-nwy3l013m007-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-nwy3l013m007-3-vpc.alikafka.aliyuncs.com:9092
  topic: data-common-collect-server
