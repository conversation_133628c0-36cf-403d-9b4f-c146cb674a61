env: prod
# ae客户端配置
ae_client:
  app_key: "24888919"
  app_secret: "f2ad91ec013832e4091508fded02eb63"
  timeout: 10s
# mysql配置
mysql:
  dsn: "supplier_aliexpress:wRcaGIrQFVvtOvVT1p2YxaMYOf713jQk@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/supplier_aliexpress?charset=utf8mb4&parseTime=True&loc=Local&timeout=5s"
mysql_big:
  dsn: supplier_aliexpress:wRcaGIrQFVvtOvVT1p2YxaMYOf713jQk@tcp(pc-8vb2y8ux2u930ou60.rwlb.zhangbei.rds.aliyuncs.com:3306)/supplier_aliexpress?charset=utf8mb4&parseTime=True&loc=Local
# 服务配置
aliexpress_product_core:
  address: "aliexpress-product-core.dsers-app.svc.cluster.local:9000"
  timeout: 15s
aliexpress_user_core:
  address: "aliexpress-user-core.dsers-app.svc.cluster.local:9000"
  timeout: 15s
tmall_currency_core:
  address: "tmall-currency-core.dsers-app.svc.cluster.local:9000"
  timeout: 15s
# redis配置
redis:
  addr: "r-8vb6rjtjj55hwggyny.redis.zhangbei.rds.aliyuncs.com:6379"
  username: "r-8vb6rjtjj55hwggyny"
  password: "9Ammg7fJOwzDU"
  db: 0
dsers_order_core:
  address: "dsers-order-core.dsers-app.svc.cluster.local:9000"
  timeout: 15s
dsers_sdk:
  key: uleigh0ANgeiphuoTaw9
  secret: mei1iZ2elieB8rubeequephie4de7aihie3ro1ophaifi1mahl
  token_url: http://open-dsers-supplier-api-gw.topdsers.com/api/v1/oauth2/token
  host: open-dsers-supplier-api-gw.topdsers.com
  schema: http   
kafka:
  addrs:
    - alikafka-pre-cn-zvp2uypcn00d-1-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-zvp2uypcn00d-2-vpc.alikafka.aliyuncs.com:9092
    - alikafka-pre-cn-zvp2uypcn00d-3-vpc.alikafka.aliyuncs.com:9092
plug_notify_url:
  "https://open.feishu.cn/open-apis/bot/v2/hook/3e81703d-29c1-4722-8ae7-b32f7277ea82"
temu_order_core:
  address: "temu-order-core.dsers-app.svc.cluster.local:9000"
  timeout: 15s  