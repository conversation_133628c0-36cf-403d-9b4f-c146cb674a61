
# env 配置文件的环境
# 目前支持以下三种形式:
#   local(谨慎使用, 没有格式)
#   test
#   prod
env: prod
server:
  http:
    addr: 0.0.0.0:8000
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 100s
  profiling:
    addr: 0.0.0.0:6060
    timeout: 100s
  metric:
    addr: 0.0.0.0:8080
    timeout: 100s
sentry:
  dsn: http://16df01f2bd13422b853924103154f9f7@172.16.16.209:9000/23
trace:
  endpoint: http://tracing-analysis-dc-hz.aliyuncs.com/adapt_ivrza9s269@31a35273955b6b1_ivrza9s269@53df7ad2afe8301/api/traces
# 数据库设置
big_mysql:
  dsn: seller_tiktokvideos:2nSnyFYzxxFa1m@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_tiktokvideos?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
mysql:
  dsn: seller_tiktokvideos:2nSnyFYzxxFa1m@tcp(pc-8vb0r35j961o8q5k2.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/seller_tiktokvideos?charset=utf8mb4&parseTime=True&loc=Local
  debug: true
# 应用设置
app_id: 1889864957596729344
# 中台SDK设置
dsers_sdk:
  scheme: http
  host: open-dsers-seller-api-gw.topdsers.com
  client_id: "1889864957596729344"
  client_secret: JFppHzuu7r17D11j2z0HQbyt3qzGCreFz2M5ZldzHvW06XtUBAw8QtV12IbkTgA=
  token_url: http://open-dsers-seller-api-gw.topdsers.com/api/v1/oauth2/token
# 服务设置
tiktokvideos_api_proxy:
  address: tiktokvideos-api-proxy.dsers-app.svc.cluster.local:9000
  timeout: 100s
dsers_product_core:
  address: dsers-product-core.dsers-app.svc.cluster.local:9000
  timeout: 100s
