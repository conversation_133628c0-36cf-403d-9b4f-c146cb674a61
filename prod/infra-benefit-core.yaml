database:
  benefit_core:
    dsn: infra_benefit_core:OzxH69DWgNSK59@tcp(pc-8vbyc6j8hzf8k6e1y.mysql.polardb.zhangbei.rds.aliyuncs.com:3306)/infra_benefit_core?loc=Local&parseTime=true&charset=utf8mb4
    debug: true

client:
  id_generator_client:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 20s

dispatcher:
  name: infra-benefit-calc
  kafka_consumer:
    consumer_num: 1
    concurrent_num: 1
  database:
    dsn: infra_dispatch_core:fh09tjPHjaFbNf@tcp(pc-8vbyc6j8hzf8k6e1y.rwlb.zhangbei.rds.aliyuncs.com:3306)/infra_dispatch_core?charset=utf8mb4&parseTime=True&loc=Local
  id_generator:
    address: infra-id-generator.dsers-app.svc.cluster.local:9000
    timeout: 20s
  redis:
    addr: "r-8vb7svnsxsrjb1wwze.redis.zhangbei.rds.aliyuncs.com:6379"
    username: "r-8vb7svnsxsrjb1wwze"
    password: "Z7c8NJXYEqbAT84okvNWCEuUGehP3BZG"
    db: 5
    dial_timeout: 5s

